package so.appio.app.utils

import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.util.Log
import androidx.core.app.NotificationCompat
import com.google.firebase.messaging.FirebaseMessaging
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import so.appio.app.MainActivity
import so.appio.app.R
import so.appio.app.widgets.AppioWidgetUpdateWorker
import so.appio.app.data.DeviceDataStore
import so.appio.app.ui.screens.MainViewModel
import so.appio.app.data.database.DatabaseManager
import so.appio.app.data.entity.notification.Notification
import java.util.UUID
import java.util.Date

class FirebaseMessagingService : FirebaseMessagingService() {
    companion object {
        private const val TAG = "LOG:MyFirebaseMsgService"

        fun refreshToken(context: Context) {
            Log.d(TAG, "Token refresh triggered")

            if (!GooglePlayServices.isGooglePlayServicesAvailable(context)) {
                Log.w(TAG, "Google Play Services not available, FCM token disabled")
                storeToken(context, "")
                return
            }

            if (!GooglePlayServices.isFirebaseReady()) {
                Log.w(TAG, "Firebase not ready, FCM token disabled")
                storeToken(context, "")
                return
            }

            FirebaseMessaging.getInstance().token.addOnCompleteListener { task ->
                if (!task.isSuccessful) {
                    Log.w(TAG, "Fetching FCM registration token failed", task.exception)
                    return@addOnCompleteListener
                }

                // Get new FCM registration token
                val token = task.result
                Log.d(TAG, "Current FCM Token: $token")

                storeToken(context, token)
            }
        }

        private fun storeToken(context: Context, token: String) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val deviceDataStore = DeviceDataStore(context)
                    val currentToken = deviceDataStore.deviceToken.first()

                    if (token != currentToken) {
                        deviceDataStore.updateDeviceToken(token)
                        Log.d(TAG, "Device token updated successfully: $token")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to store Device token", e)
                }
            }
        }
    }

    /**
     * Called when a message is received.
     *
     * @param remoteMessage Object representing the message received from Firebase Cloud Messaging.
     */
    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        // This method is called for ALL data-only messages, regardless of app state
        Log.d(TAG, "From: ${remoteMessage.from}")

        // Handle data payload (this is where our custom notification logic runs)
        remoteMessage.data.isNotEmpty().let {
            Log.d(TAG, "Message data payload: " + remoteMessage.data)

            // Notification data
            val title = remoteMessage.data["title"]
            val body = remoteMessage.data["body"]
            val serviceId = remoteMessage.data["service_id"]
            val serviceTitle = remoteMessage.data["service_title"]
            var logoUrl = remoteMessage.data["logo_url"]
            var bannerUrl = remoteMessage.data["banner_url"]
            var category = remoteMessage.data["category"] ?: NotificationCompat.CATEGORY_MESSAGE
            var actionCategory = remoteMessage.data["action_category"] // unlike in iOS, the buttons could be completely custom in Android.

            if (title.isNullOrEmpty() ||
                body.isNullOrEmpty() ||
                serviceId.isNullOrEmpty() ||
                logoUrl.isNullOrEmpty()
            ) {
                Log.w(TAG, "Missing required notification data")
                remoteMessage.data.forEach { (key, value) ->
                    Log.d(TAG, "key = $key, value = $value")
                }

                return
            }

            sendNotification(
                serviceId,
                serviceTitle,
                logoUrl,
                bannerUrl,
                title,
                body,
                category,
                actionCategory
            )
        }

        // Handle notification payload (fallback - should not be used with data-only messages)
        remoteMessage.notification?.let {
            Log.d(TAG, "Message Notification Body: ${it.body}")
            Log.w(
                TAG,
                "Received notification payload - this may not show custom icon when app is background"
            )
            // TODO: what to do?
        }

        // Also update widgets each time any type of notification is received
        AppioWidgetUpdateWorker.updateWidgetsForEvent(
            this,
            AppioWidgetUpdateWorker.REASON_NOTIFICATION_RECEIVED
        )
    }

    /**
     * Called if the FCM registration token is updated. This may occur if the security of
     * the previous token had been compromised. Note that this is called when the
     * FCM registration token is initially generated so this is where you would retrieve the token.
     */
    override fun onNewToken(token: String) {
        Log.d(TAG, "Refreshed token: $token")
        storeToken(this@FirebaseMessagingService, token)
    }

    private fun sendNotification(
        serviceId: String,
        serviceTitle: String?,
        logoUrl: String,
        mediaUrl: String?,
        title: String,
        body: String,
        category: String,
        actionCategory: String?,
    ) {
        Log.d(TAG, "Sending notification: Title: $title, Body: $body")

        CoroutineScope(Dispatchers.IO).launch {
            val serviceLogo = ImageCacheManager.getCachedImage(
                url = logoUrl
            )
            val mediaImage = mediaUrl?.let {
                ImageCacheManager.getCachedImage(
                    url = it
                )
            }

            showNotificationWithIcon(
                serviceId,
                serviceTitle,
                serviceLogo,
                mediaImage,
                title,
                body,
                category,
                actionCategory,
            )
        }
    }

    private fun showNotificationWithIcon(
        serviceId: String,
        serviceTitle: String?,
        icon: Bitmap?,
        mediaImage: Bitmap?,
        title: String,
        body: String,
        category: String,
        actionCategory: String?
    ) {
        val notificationId = System.currentTimeMillis().toInt()

        val intent = PendingIntent.getActivity(
            this,
            0,
            Intent(this, MainActivity::class.java).apply {
                action = IntentHandler.IntentAction.NOTIFICATION.value
                flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
                putExtra("service_id", serviceId)
            },
            PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_ONE_SHOT
        )

        val channelId = NotificationChannels.createChannelId(serviceId) // Assumed channelId is already registered in MyApplication.kt
        val notificationBuilder = NotificationCompat.Builder(this, channelId)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle(title)
            .setContentText(body)
            .setSubText(serviceTitle) // Limited extra title, ideal for Service Title
            .setSettingsText("$serviceTitle Settings") // Provides text that MAY appear as a link to your app's settings. Limit 40 characters
            .setCategory(category)

            .setAutoCancel(true) // notification will be removed from "notification drawer" once tapped. otherwise it stays there and we need to trigger removal from MainActivity
            .setContentIntent(intent)
            .setPriority(NotificationCompat.PRIORITY_MAX) // show, vibrate, sound
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC) // show content on locked screen
            .setColor(0xFF000000.toInt())
            .setColorized(true) // Default app icon (Appio) background in pop-up view

        // Obsolete
        // .setContentInfo() // Superseded by setSubText
        // .setSound() // controlled by channel

        // Not finished. Custom views.  Defaults are fine.
        // .setCustomHeadsUpContentView(customPopup(title, body, icon)) // pop-up view
        // .setCustomBigContentView() // pull-down view

        // Service logo
        icon?.let {
            notificationBuilder.setLargeIcon(it)
        } ?: Log.e(TAG, "Notification failed to load service logo")

        // Media image
        mediaImage?.let {
            notificationBuilder.setStyle(
                NotificationCompat.BigPictureStyle()
                    .bigPicture(mediaImage)
                // .setBigContentTitle(title) // extra: when expanded, custom title
                // .setSummaryText(body) // extra: when expanded, custom text
            )
        } ?: Log.e(TAG, "Notification failed to load media image")

        // Custom buttons
        actionCategory?.let {
            when (it) {
                "APPIO_YES_NO" -> addDynamicActionButtons(
                    notificationBuilder,
                    notificationId,
                    serviceId,
                    listOf(
                        ActionButton("Yes", "yes"),
                        ActionButton("No", "no"),
                    )
                )
            }
        }

        // Send notification
        val notificationManager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(notificationId, notificationBuilder.build())
        Log.d(TAG, "Notification sent: Channel: $channelId, Title: $title, Body: $body")
    }

//    private fun customPopup(title: String, body: String, icon: Bitmap?): RemoteViews {
//        val view = RemoteViews(packageName, R.layout.notification_heads_up)
//        view.setImageViewBitmap(R.id.icon, icon)
//        view.setTextViewText(R.id.title, title)
//        view.setTextViewText(R.id.text, body)
//        return view
//    }

    private data class ActionButton(
        val title: String,
        val id: String,
        val icon: String = "",
    )

    private fun addDynamicActionButtons(
        notificationBuilder: NotificationCompat.Builder,
        notificationId: Int,
        serviceId: String,
        buttons: List<ActionButton>,
    ) {
        buttons.forEachIndexed { index, item ->
            Log.d(TAG, "Adding action button: ${item.title}, ${item.id}")

            val actionIntent = PendingIntent.getActivity(
                this,
                index + 1, // to avoid using 0, that is reserved for main notification intent
                Intent(this, MainActivity::class.java).apply {
                    action = IntentHandler.IntentAction.NOTIFICATION.value
                    putExtra("notification_id", notificationId)
                    putExtra("notification_action_button_id", item.id)
                    putExtra("service_id", serviceId)
                },
                PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
            )
            notificationBuilder
                // To render icon, change `0` to R.drawable.ic_app
                .addAction(getDrawableId(item.icon), item.title, actionIntent)
        }
    }

    private fun getDrawableId(drawableName: String): Int {
        if (drawableName == "") {
            return 0
        }
        return resources.getIdentifier(drawableName, "drawable", packageName)
    }
}