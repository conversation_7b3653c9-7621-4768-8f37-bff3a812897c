#!/opt/homebrew/bin/bash

# ==============================================================================
# Firebase Push Notification Script
# ==============================================================================
# This script sends push notifications to your Android emulator app via Firebase
#
# Usage:
#   - ./send_dev.sh "***_fcm_token_***"
#   - ./send_dev.sh          // this will use FCM_TOKEN constant from the script
#
# ==============================================================================

# Configuration Constants
readonly FIREBASE_PROJECT_ID="appio-so"
readonly SERVICE_ACCOUNT_FILE=".keys/firebase-dev.json"

# Custom Data
declare -A CUSTOM_DATA
CUSTOM_DATA["service_id"]="demo_svc_01jwbqdwte4typkr18b85e3mpp"
CUSTOM_DATA["service_title"]="Zesty Service"
CUSTOM_DATA["logo_url"]="https://cdn.appio.so/app/demo.appio.so/logo.png"
CUSTOM_DATA["banner_url"]="https://cdn.appio.so/app/demo.appio.so/banner.jpg"
#CUSTOM_DATA["category"]="msg"
#CUSTOM_DATA["action_category"]="APPIO_YES_NO"

# Optional
#FCM_TOKEN=""

# ==============================================================================

# Source the core functionality
source "$(dirname "$0")/send_core.sh"

# Run the main function with all arguments
main "$@"